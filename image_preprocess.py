# collect_left_images.py
import os
from pathlib import Path
import shutil
import argparse
from tqdm import tqdm

IMAGE_EXTS = {".jpg", ".jpeg", ".png", ".bmp", ".tif", ".tiff"}

def main():
    parser = argparse.ArgumentParser(
        description="在当前 road 的输入目录中收集 ColorImage/**/Camera 5 下所有图片到扁平目录 leftImg8bit/，并用 road_id 前缀命名"
    )
    parser.add_argument("input_root", help="输入根目录（该目录只含本 road 的 ColorImage/）")
    parser.add_argument("--road_id", required=True, help="如 road02 / road03，用于输出文件名前缀")
    parser.add_argument("--camera", default="Camera 5", help="要收集的相机文件夹名，默认 Camera 5")
    parser.add_argument("--out_dir", default="leftImg8bit", help="输出目录（单层扁平化）")
    args = parser.parse_args()

    input_root = Path(args.input_root)
    out_dir = Path(args.out_dir)
    out_dir.mkdir(parents=True, exist_ok=True)

    
    # patterns = [
    #     input_root.glob(f"ColorImage/*/{args.camera}/*"),
    # ]

    # all_imgs = []
    # for g in patterns:
    #     for p in g:
    #         if p.is_file() and p.suffix.lower() in IMAGE_EXTS:
    #             all_imgs.append(p)

    root_ci = input_root / "ColorImage"
    all_imgs = sorted({
        p.resolve()
        for p in root_ci.glob(f"*/{args.camera}/**/*")
        if p.is_file() and p.suffix.lower() in IMAGE_EXTS and p.parent.name == args.camera
    })
    print('图片个数： ', len(all_imgs))

    pbar = tqdm(all_imgs, desc=f"拷贝 {args.camera} 图片到 {out_dir}")
    for src in pbar:
        parts = src.parts
        # 期望结构: .../ColorImage/Record***/Camera 5/filename
        try:
            ci_idx = parts.index("ColorImage")
            record = parts[ci_idx + 1] if ci_idx + 1 < len(parts) else "RecordX"
        except ValueError:
            record = "RecordX"

        new_name = f"{args.road_id}_{record}_{args.camera.replace(' ', '')}_{src.name}"
        dst = out_dir / new_name
        if not dst.exists():
            shutil.copy2(src, dst)

    print(f"完成：共收集 {len(all_imgs)} 张图片到 {out_dir}")

if __name__ == "__main__":
    main()
